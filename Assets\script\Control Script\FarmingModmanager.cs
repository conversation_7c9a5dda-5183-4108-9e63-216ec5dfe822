

using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;

public class FarmingModmanager : MonoBehaviour
{
    public GameObject[] level;
    public Transform[] Position;
    public GameObject Tractor;
    public GameObject Steeringactive,Buttonsactive,Tiltactive;
    public GameObject Steeriginactive,Buttonsinactive,Tiltinactive;
   

    void Start()
    {
        InitializeLevels();
        level[MainMenu.levlno].SetActive(true);
    }

    void InitializeLevels()
    {
        Tractor.transform.position = Position[MainMenu.levlno].position;
        Tractor.transform.rotation = Position[MainMenu.levlno].rotation;
    }

    public void NextLevel()
    {
        MainMenu.CompleteFarmLevel(MainMenu.levlno);

        if (MainMenu.levlno < level.Length - 1)
        {
            MainMenu.levlno++;
            PlayerPrefs.SetInt("Farm" + MainMenu.levlno, 1);
            PlayerPrefs.Save();
            SceneManager.LoadScene("Farming mod");
        }
        else
        {
            SceneManager.LoadScene("MAINMENU");
        }
    }
    public void Restart()
    {
        SceneManager.LoadScene("Farming mod");
    }
    public void Home()
    {
        SceneManager.LoadScene("MAINMENU");
    }
    public void Resume()
    {
        Time.timeScale = 1;
    }
    public void Pause()
    {
        Time.timeScale = 0;
    }
    public void Steering()
    {
       RCC.SetMobileController(RCC_Settings.MobileController.SteeringWheel);

       
       Steeringactive.SetActive(true);
       Steeriginactive.SetActive(false);

       
       Buttonsactive.SetActive(false);
       Buttonsinactive.SetActive(true);

       
       Tiltactive.SetActive(false);
       Tiltinactive.SetActive(true);
    }
    public void Buttons()
    {
          RCC.SetMobileController(RCC_Settings.MobileController.TouchScreen);

          
          Buttonsactive.SetActive(true);
          Buttonsinactive.SetActive(false);

          
          Steeringactive.SetActive(false);
          Steeriginactive.SetActive(true);

          
          Tiltactive.SetActive(false);
          Tiltinactive.SetActive(true);
    }
    public void Tilt()
    {
          RCC.SetMobileController(RCC_Settings.MobileController.Gyro);

          
          Tiltactive.SetActive(true);
          Tiltinactive.SetActive(false);

          
          Steeringactive.SetActive(false);
          Steeriginactive.SetActive(true);

          
          Buttonsactive.SetActive(false);
          Buttonsinactive.SetActive(true);
    }
}
